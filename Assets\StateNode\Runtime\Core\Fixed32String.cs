using System;
using Unity.Collections;
using Unity.Collections.LowLevel.Unsafe;

namespace StateScript
{
    /// <summary>
    /// Stack-allocated fixed-size string for performance-critical scenarios
    /// </summary>
    [System.Serializable]
    public unsafe struct Fixed32String : IEquatable<Fixed32String>
    {
        private fixed byte bytes[32];
        
        public Fixed32String(string value)
        {
            fixed (byte* ptr = bytes)
            {
                var span = new Span<byte>(ptr, 32);
                span.Clear();
                if (!string.IsNullOrEmpty(value))
                {
                    var encoded = System.Text.Encoding.UTF8.GetBytes(value);
                    var length = Math.Min(encoded.Length, 31); // Reserve 1 byte for null terminator
                    encoded.AsSpan(0, length).CopyTo(span);
                }
            }
        }
        
        public override string ToString()
        {
            fixed (byte* ptr = bytes)
            {
                var span = new ReadOnlySpan<byte>(ptr, 32);
                var nullIndex = span.IndexOf((byte)0);
                if (nullIndex >= 0)
                    span = span.Slice(0, nullIndex);
                return System.Text.Encoding.UTF8.GetString(span);
            }
        }
        
        public bool Equals(Fixed32String other)
        {
            fixed (byte* thisPtr = bytes)
            fixed (byte* otherPtr = other.bytes)
            {
                return new ReadOnlySpan<byte>(thisPtr, 32).SequenceEqual(new ReadOnlySpan<byte>(otherPtr, 32));
            }
        }
        
        public override bool Equals(object obj) => obj is Fixed32String other && Equals(other);
        
        public override int GetHashCode()
        {
            fixed (byte* ptr = bytes)
            {
                var span = new ReadOnlySpan<byte>(ptr, 32);
                var nullIndex = span.IndexOf((byte)0);
                if (nullIndex >= 0)
                    span = span.Slice(0, nullIndex);
                
                // Simple hash for performance
                int hash = 17;
                for (int i = 0; i < span.Length; i++)
                {
                    hash = hash * 31 + span[i];
                }
                return hash;
            }
        }
        
        public static implicit operator string(Fixed32String fixedString) => fixedString.ToString();
        public static implicit operator Fixed32String(string str) => new Fixed32String(str);
        
        public static bool operator ==(Fixed32String left, Fixed32String right) => left.Equals(right);
        public static bool operator !=(Fixed32String left, Fixed32String right) => !left.Equals(right);
    }
}

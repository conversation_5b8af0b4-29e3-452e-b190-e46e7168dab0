using UnityEngine;
using UnityEditor;
using UnityEngine.UIElements;
using UnityEditor.UIElements;
using UnityEditor.Experimental.GraphView;

namespace StateScript.Editor
{
    /// <summary>
    /// Test script to verify Unity 6000 UI Toolkit compatibility
    /// This can be removed after testing
    /// </summary>
    public static class Unity6000CompatibilityTest
    {
        [MenuItem("StateScript/Test Unity 6000 Compatibility")]
        public static void TestCompatibility()
        {
            Debug.Log("Testing Unity 6000 UI Toolkit compatibility...");
            
            try
            {
                // Test basic UI Elements
                var button = new Button();
                var toolbar = new Toolbar();
                var colorField = new ColorField();
                var integerField = new IntegerField();
                var floatField = new FloatField();
                var toggle = new Toggle();
                var textField = new TextField();
                var vector3Field = new Vector3Field();
                
                // Test GenericMenu
                var menu = new GenericMenu();
                menu.AddItem(new GUIContent("Test"), false, () => { });
                
                // Test GraphView components (basic instantiation)
                // Note: These need a proper parent to work fully
                var testWindow = EditorWindow.CreateInstance<TestWindow>();
                testWindow.TestGraphViewComponents();
                testWindow.Close();
                
                Debug.Log("✅ Unity 6000 UI Toolkit compatibility test PASSED!");
                EditorUtility.DisplayDialog("Compatibility Test", 
                    "Unity 6000 UI Toolkit compatibility test PASSED!\n\nAll required components are accessible.", 
                    "OK");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ Unity 6000 UI Toolkit compatibility test FAILED: {e.Message}");
                EditorUtility.DisplayDialog("Compatibility Test Failed", 
                    $"Unity 6000 UI Toolkit compatibility test FAILED:\n\n{e.Message}", 
                    "OK");
            }
        }
    }
    
    /// <summary>
    /// Temporary test window for GraphView components
    /// </summary>
    public class TestWindow : EditorWindow
    {
        public void TestGraphViewComponents()
        {
            try
            {
                var root = rootVisualElement;
                
                // Test GraphView
                var graphView = new TestGraphView();
                root.Add(graphView);
                
                // Test basic GraphView setup
                graphView.SetupZoom(ContentZoomer.DefaultMinScale, ContentZoomer.DefaultMaxScale);
                graphView.AddManipulator(new ContentDragger());
                graphView.AddManipulator(new SelectionDragger());
                graphView.AddManipulator(new RectangleSelector());
                
                var grid = new GridBackground();
                graphView.Insert(0, grid);
                
                Debug.Log("✅ GraphView components test passed");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ GraphView components test failed: {e.Message}");
                throw;
            }
        }
    }
    
    /// <summary>
    /// Minimal GraphView for testing
    /// </summary>
    public class TestGraphView : GraphView
    {
        public TestGraphView()
        {
            // Basic setup to test GraphView instantiation
        }
        
        public override List<Port> GetCompatiblePorts(Port startPort, NodeAdapter nodeAdapter)
        {
            return new List<Port>();
        }
    }
}

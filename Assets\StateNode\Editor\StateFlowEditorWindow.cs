using UnityEngine;
using UnityEditor;
using UnityEngine.UIElements;
using UnityEditor.Experimental.GraphView;
using StateScript;

namespace StateScript.Editor
{
    /// <summary>
    /// Main editor window for editing StateFlow graphs using Unity's GraphView
    /// </summary>
    public class StateFlowEditorWindow : EditorWindow
    {
        private StateFlowAsset _currentAsset;
        private StateFlowGraphView _graphView;
        private StateFlowInspectorView _inspectorView;
        private StateFlowVariableView _variableView;

        [MenuItem("Window/StateScript/StateFlow Editor")]
        public static void OpenWindow()
        {
            var window = GetWindow<StateFlowEditorWindow>();
            window.titleContent = new GUIContent("StateFlow Editor");
            window.Show();
        }

        public static void OpenWindow(StateFlowAsset asset)
        {
            var window = GetWindow<StateFlowEditorWindow>();
            window.titleContent = new GUIContent($"StateFlow Editor - {asset.AssetName}");
            window.LoadAsset(asset);
            window.Show();
        }

        private void CreateGUI()
        {
            // Create the main container
            var root = rootVisualElement;
            CreateGUIManually(root);
            InitializeViews();
        }

        private void CreateGUIManually(VisualElement root)
        {
            // Create main layout
            var mainContainer = new VisualElement();
            mainContainer.style.flexDirection = FlexDirection.Row;
            mainContainer.style.flexGrow = 1;

            // Left panel for variables and inspector
            var leftPanel = new VisualElement();
            leftPanel.style.width = 300;
            leftPanel.style.backgroundColor = new Color(0.2f, 0.2f, 0.2f, 1f);
            leftPanel.name = "left-panel";

            // Graph view container
            var graphContainer = new VisualElement();
            graphContainer.style.flexGrow = 1;
            graphContainer.name = "graph-container";

            // Add toolbar
            var toolbar = new Toolbar();
            var saveButton = new ToolbarButton(() => SaveAsset()) { text = "Save" };
            var loadButton = new ToolbarButton(() => LoadAssetDialog()) { text = "Load" };
            var newButton = new ToolbarButton(() => CreateNewAsset()) { text = "New" };

            toolbar.Add(newButton);
            toolbar.Add(loadButton);
            toolbar.Add(saveButton);

            root.Add(toolbar);
            root.Add(mainContainer);
            mainContainer.Add(leftPanel);
            mainContainer.Add(graphContainer);
        }

        private void InitializeViews()
        {
            var root = rootVisualElement;

            // Find containers
            var leftPanel = root.Q("left-panel");
            var graphContainer = root.Q("graph-container");

            if (leftPanel == null || graphContainer == null)
            {
                Debug.LogError("Could not find required UI containers");
                return;
            }

            // Create graph view
            _graphView = new StateFlowGraphView(this);
            _graphView.StretchToParentSize();
            graphContainer.Add(_graphView);

            // Create variable view
            _variableView = new StateFlowVariableView();
            leftPanel.Add(_variableView);

            // Create inspector view
            _inspectorView = new StateFlowInspectorView();
            leftPanel.Add(_inspectorView);

            // Connect events
            _graphView.OnNodeSelected += _inspectorView.InspectNode;
            _variableView.OnVariableChanged += OnVariableChanged;
        }

        public void LoadAsset(StateFlowAsset asset)
        {
            _currentAsset = asset;

            if (_graphView != null)
            {
                _graphView.LoadStateFlow(asset.StateFlow);
            }

            if (_variableView != null)
            {
                _variableView.LoadVariables(asset.StateFlow.Variables);
            }

            titleContent = new GUIContent($"StateFlow Editor - {asset.AssetName}");
        }

        private void SaveAsset()
        {
            if (_currentAsset != null)
            {
                _currentAsset.SerializeStateFlow();
                EditorUtility.SetDirty(_currentAsset);
                AssetDatabase.SaveAssets();
                Debug.Log($"Saved StateFlow: {_currentAsset.AssetName}");
            }
        }

        private void LoadAssetDialog()
        {
            var path = EditorUtility.OpenFilePanel("Load StateFlow", "Assets", "asset");
            if (!string.IsNullOrEmpty(path))
            {
                // Convert absolute path to relative
                if (path.StartsWith(Application.dataPath))
                {
                    path = "Assets" + path.Substring(Application.dataPath.Length);
                }

                var asset = AssetDatabase.LoadAssetAtPath<StateFlowAsset>(path);
                if (asset != null)
                {
                    LoadAsset(asset);
                }
            }
        }

        private void CreateNewAsset()
        {
            var asset = CreateInstance<StateFlowAsset>();
            var path = EditorUtility.SaveFilePanelInProject("Create StateFlow", "New StateFlow", "asset", "Create new StateFlow asset");

            if (!string.IsNullOrEmpty(path))
            {
                AssetDatabase.CreateAsset(asset, path);
                AssetDatabase.SaveAssets();
                LoadAsset(asset);
            }
        }

        private void OnVariableChanged()
        {
            if (_currentAsset != null)
            {
                EditorUtility.SetDirty(_currentAsset);
            }
        }

        private void OnDisable()
        {
            // Save any pending changes
            if (_currentAsset != null && _currentAsset.IsDirty)
            {
                if (EditorUtility.DisplayDialog("Unsaved Changes",
                    "You have unsaved changes. Do you want to save before closing?",
                    "Save", "Don't Save"))
                {
                    SaveAsset();
                }
            }
        }
    }
}
        
        private void CreateGUI()
        {
            // Create the main container
            var root = rootVisualElement;
            
            // Load UXML and USS
            var visualTree = AssetDatabase.LoadAssetAtPath<VisualTreeAsset>("Assets/StateNode/Editor/StateFlowEditor.uxml");
            if (visualTree != null)
            {
                visualTree.CloneTree(root);
            }
            else
            {
                CreateGUIManually(root);
            }
            
            var styleSheet = AssetDatabase.LoadAssetAtPath<StyleSheet>("Assets/StateNode/Editor/StateFlowEditor.uss");
            if (styleSheet != null)
            {
                root.styleSheets.Add(styleSheet);
            }
            
            // Initialize views
            InitializeViews();
        }
        
        private void CreateGUIManually(VisualElement root)
        {
            // Create main layout
            var mainContainer = new VisualElement();
            mainContainer.style.flexDirection = FlexDirection.Row;
            mainContainer.style.flexGrow = 1;
            
            // Left panel for variables and inspector
            var leftPanel = new VisualElement();
            leftPanel.style.width = 300;
            leftPanel.style.backgroundColor = new Color(0.2f, 0.2f, 0.2f, 1f);
            
            // Graph view container
            var graphContainer = new VisualElement();
            graphContainer.style.flexGrow = 1;
            
            // Add toolbar
            var toolbar = new Toolbar();
            var saveButton = new ToolbarButton(() => SaveAsset()) { text = "Save" };
            var loadButton = new ToolbarButton(() => LoadAssetDialog()) { text = "Load" };
            var newButton = new ToolbarButton(() => CreateNewAsset()) { text = "New" };
            
            toolbar.Add(newButton);
            toolbar.Add(loadButton);
            toolbar.Add(saveButton);
            
            root.Add(toolbar);
            root.Add(mainContainer);
            mainContainer.Add(leftPanel);
            mainContainer.Add(graphContainer);
            
            // Store references for initialization
            leftPanel.name = "left-panel";
            graphContainer.name = "graph-container";
        }
        
        private void InitializeViews()
        {
            var root = rootVisualElement;
            
            // Find containers
            var leftPanel = root.Q("left-panel") ?? root.Q<VisualElement>("left-panel");
            var graphContainer = root.Q("graph-container") ?? root.Q<VisualElement>("graph-container");
            
            if (leftPanel == null || graphContainer == null)
            {
                Debug.LogError("Could not find required UI containers");
                return;
            }
            
            // Create graph view
            _graphView = new StateFlowGraphView(this);
            _graphView.StretchToParentSize();
            graphContainer.Add(_graphView);
            
            // Create variable view
            _variableView = new StateFlowVariableView();
            leftPanel.Add(_variableView);
            
            // Create inspector view
            _inspectorView = new StateFlowInspectorView();
            leftPanel.Add(_inspectorView);
            
            // Connect events
            _graphView.OnNodeSelected += _inspectorView.InspectNode;
            _variableView.OnVariableChanged += OnVariableChanged;
        }
        
        public void LoadAsset(StateFlowAsset asset)
        {
            _currentAsset = asset;
            
            if (_graphView != null)
            {
                _graphView.LoadStateFlow(asset.StateFlow);
            }
            
            if (_variableView != null)
            {
                _variableView.LoadVariables(asset.StateFlow.Variables);
            }
            
            titleContent = new GUIContent($"StateFlow Editor - {asset.AssetName}");
        }
        
        private void SaveAsset()
        {
            if (_currentAsset != null)
            {
                _currentAsset.SerializeStateFlow();
                EditorUtility.SetDirty(_currentAsset);
                AssetDatabase.SaveAssets();
                Debug.Log($"Saved StateFlow: {_currentAsset.AssetName}");
            }
        }
        
        private void LoadAssetDialog()
        {
            var path = EditorUtility.OpenFilePanel("Load StateFlow", "Assets", "asset");
            if (!string.IsNullOrEmpty(path))
            {
                // Convert absolute path to relative
                if (path.StartsWith(Application.dataPath))
                {
                    path = "Assets" + path.Substring(Application.dataPath.Length);
                }
                
                var asset = AssetDatabase.LoadAssetAtPath<StateFlowAsset>(path);
                if (asset != null)
                {
                    LoadAsset(asset);
                }
            }
        }
        
        private void CreateNewAsset()
        {
            var asset = CreateInstance<StateFlowAsset>();
            var path = EditorUtility.SaveFilePanelInProject("Create StateFlow", "New StateFlow", "asset", "Create new StateFlow asset");
            
            if (!string.IsNullOrEmpty(path))
            {
                AssetDatabase.CreateAsset(asset, path);
                AssetDatabase.SaveAssets();
                LoadAsset(asset);
            }
        }
        
        private void OnVariableChanged()
        {
            if (_currentAsset != null)
            {
                EditorUtility.SetDirty(_currentAsset);
            }
        }
        
        private void OnDisable()
        {
            // Save any pending changes
            if (_currentAsset != null && _currentAsset.IsDirty)
            {
                if (EditorUtility.DisplayDialog("Unsaved Changes", 
                    "You have unsaved changes. Do you want to save before closing?", 
                    "Save", "Don't Save"))
                {
                    SaveAsset();
                }
            }
        }
    }
}

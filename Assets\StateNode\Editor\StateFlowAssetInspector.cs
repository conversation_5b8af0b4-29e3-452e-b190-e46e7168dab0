using UnityEngine;
using UnityEditor;
using StateScript;

namespace StateScript.Editor
{
    /// <summary>
    /// Custom inspector for StateFlowAsset that shows a button to open the node editor
    /// </summary>
    [CustomEditor(typeof(StateFlowAsset))]
    public class StateFlowAssetInspector : UnityEditor.Editor
    {
        private StateFlowAsset _asset;
        
        private void OnEnable()
        {
            _asset = target as StateFlowAsset;
        }
        
        public override void OnInspectorGUI()
        {
            if (_asset == null)
                return;
            
            EditorGUILayout.Space();
            
            // Asset name field
            EditorGUI.BeginChangeCheck();
            var newName = EditorGUILayout.TextField("Asset Name", _asset.AssetName);
            if (EditorGUI.EndChangeCheck())
            {
                _asset.AssetName = newName;
                EditorUtility.SetDirty(_asset);
            }
            
            EditorGUILayout.Space();
            
            // StateFlow info
            var stateFlow = _asset.StateFlow;
            if (stateFlow != null)
            {
                EditorGUILayout.LabelField("StateFlow Info", EditorStyles.boldLabel);
                EditorGUILayout.LabelField($"Status: {stateFlow.Status}");
                EditorGUILayout.LabelField($"Nodes: {stateFlow.Nodes.Count}");
                EditorGUILayout.LabelField($"Connections: {stateFlow.Connections.Count}");
                EditorGUILayout.LabelField($"Variables: {stateFlow.Variables.Count}");
                
                if (stateFlow.EntryNodeId.Value != 0)
                {
                    EditorGUILayout.LabelField($"Entry Node ID: {stateFlow.EntryNodeId}");
                }
                else
                {
                    EditorGUILayout.LabelField("Entry Node: None (uses event listeners)");
                }
            }
            
            EditorGUILayout.Space();
            
            // Validation
            if (GUILayout.Button("Validate StateFlow"))
            {
                if (_asset.Validate(out var errors))
                {
                    EditorUtility.DisplayDialog("Validation", "StateFlow is valid!", "OK");
                }
                else
                {
                    var errorMessage = "StateFlow has the following issues:\n\n" + string.Join("\n", errors);
                    EditorUtility.DisplayDialog("Validation Errors", errorMessage, "OK");
                }
            }
            
            EditorGUILayout.Space();
            
            // Main button to open node editor
            if (GUILayout.Button("Open StateFlow Editor", GUILayout.Height(40)))
            {
                StateFlowEditorWindow.OpenWindow(_asset);
            }
            
            EditorGUILayout.Space();
            
            // Debug info
            if (Application.isPlaying && stateFlow.Status == StateFlowStatus.Running)
            {
                EditorGUILayout.LabelField("Runtime Info", EditorStyles.boldLabel);
                if (stateFlow.CurrentNode != null)
                {
                    EditorGUILayout.LabelField($"Current Node: {stateFlow.CurrentNode.GetType().Name} ({stateFlow.CurrentNode.Property.Id})");
                    EditorGUILayout.LabelField($"Node Status: {stateFlow.CurrentNode.Status}");
                }
                else
                {
                    EditorGUILayout.LabelField("Current Node: None");
                }
                
                // Show variable values
                if (stateFlow.Variables.Count > 0)
                {
                    EditorGUILayout.Space();
                    EditorGUILayout.LabelField("Variable Values", EditorStyles.boldLabel);
                    foreach (var variable in stateFlow.Variables)
                    {
                        string valueStr = "Unknown";
                        switch (variable)
                        {
                            case StateVariableInt intVar:
                                valueStr = intVar.Value.ToString();
                                break;
                            case StateVariableFloat floatVar:
                                valueStr = floatVar.Value.ToString("F2");
                                break;
                            case StateVariableBool boolVar:
                                valueStr = boolVar.Value.ToString();
                                break;
                            case StateVariableString stringVar:
                                valueStr = $"\"{stringVar.Value}\"";
                                break;
                        }
                        EditorGUILayout.LabelField($"{variable.Name}: {valueStr}");
                    }
                }
                
                // Auto-refresh during play mode
                if (Application.isPlaying)
                {
                    Repaint();
                }
            }
            
            // Show dirty state
            if (_asset.IsDirty)
            {
                EditorGUILayout.HelpBox("StateFlow has unsaved changes", MessageType.Info);
            }
            
            // JSON export/import (for debugging)
            EditorGUILayout.Space();
            if (GUILayout.Button("Export JSON"))
            {
                var path = EditorUtility.SaveFilePanel("Export StateFlow JSON", "", _asset.AssetName + ".json", "json");
                if (!string.IsNullOrEmpty(path))
                {
                    _asset.SaveToFile(path);
                }
            }
            
            if (GUILayout.Button("Import JSON"))
            {
                var path = EditorUtility.OpenFilePanel("Import StateFlow JSON", "", "json");
                if (!string.IsNullOrEmpty(path))
                {
                    var importedAsset = StateFlowAsset.LoadFromFile(path);
                    _asset.StateFlow = importedAsset.StateFlow;
                    EditorUtility.SetDirty(_asset);
                }
            }
        }
    }
}

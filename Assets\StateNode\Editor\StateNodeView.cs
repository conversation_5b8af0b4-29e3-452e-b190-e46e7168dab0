using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using UnityEngine.UIElements;
using UnityEditor.Experimental.GraphView;
using StateScript;

namespace StateScript.Editor
{
    /// <summary>
    /// Visual representation of a StateNode in the GraphView
    /// </summary>
    public class StateNodeView : Node
    {
        public event Action<StateNode> OnNodeSelected;
        
        public StateNode StateNode { get; private set; }
        
        private Dictionary<PortId, StatePortView> _portViews = new Dictionary<PortId, StatePortView>();
        
        public StateNodeView(StateNode stateNode)
        {
            StateNode = stateNode;
            
            title = stateNode.EditorProperty.Name.ToString();
            
            // Set node color based on type
            SetNodeColor();
            
            // Create ports
            CreatePorts();
            
            // Handle selection
            RegisterCallback<MouseDownEvent>(OnMouseDown);
            
            // Make node resizable
            capabilities |= Capabilities.Resizable;
        }
        
        private void SetNodeColor()
        {
            var color = StateNode.EditorProperty.NodeColor;
            
            // Set default colors based on node type if not set
            if (color == Color.white)
            {
                color = StateNode switch
                {
                    StateActionNode => new Color(0.2f, 0.6f, 0.2f, 1f), // Green
                    StateConditionNode => new Color(0.6f, 0.6f, 0.2f, 1f), // Yellow
                    StateListenerNode => new Color(0.2f, 0.2f, 0.6f, 1f), // Blue
                    _ => new Color(0.4f, 0.4f, 0.4f, 1f) // Gray
                };
                StateNode.EditorProperty.NodeColor = color;
            }
            
            // Apply color to the node
            style.backgroundColor = color;
        }
        
        private void CreatePorts()
        {
            foreach (var statePort in StateNode.Property.Ports)
            {
                var portView = CreatePortView(statePort);
                _portViews[statePort.Id] = portView;
                
                if (statePort.Type == PortType.Input)
                {
                    inputContainer.Add(portView);
                }
                else
                {
                    outputContainer.Add(portView);
                }
            }
        }
        
        private StatePortView CreatePortView(StatePort statePort)
        {
            var direction = statePort.Type == PortType.Input ? Direction.Input : Direction.Output;
            var capacity = Port.Capacity.Multi; // Allow multiple connections
            
            var portView = new StatePortView(statePort, direction, capacity);
            portView.portName = statePort.Name.ToString();
            
            return portView;
        }
        
        public StatePortView GetPortView(PortId portId)
        {
            return _portViews.TryGetValue(portId, out var portView) ? portView : null;
        }
        
        private void OnMouseDown(MouseDownEvent evt)
        {
            if (evt.clickCount == 1)
            {
                OnNodeSelected?.Invoke(StateNode);
            }
            else if (evt.clickCount == 2)
            {
                // Double-click to edit node properties
                OpenNodeEditor();
            }
        }
        
        private void OpenNodeEditor()
        {
            // Create a popup window for editing node properties
            var popup = new NodeEditorPopup(StateNode);
            popup.ShowAsDropDown(worldBound, new Vector2(300, 400));
        }
        
        public override void SetPosition(Rect newPos)
        {
            base.SetPosition(newPos);
            StateNode.EditorProperty.Position = newPos.position;
        }
        
        public void UpdateTitle()
        {
            title = StateNode.EditorProperty.Name.ToString();
        }
        
        public void RefreshPorts()
        {
            // Clear existing ports
            inputContainer.Clear();
            outputContainer.Clear();
            _portViews.Clear();
            
            // Recreate ports
            CreatePorts();
        }
    }
    
    /// <summary>
    /// Custom port view for StateScript ports
    /// </summary>
    public class StatePortView : Port
    {
        public StatePort StatePort { get; private set; }
        
        public StatePortView(StatePort statePort, Direction direction, Capacity capacity) 
            : base(Orientation.Horizontal, direction, capacity, typeof(object))
        {
            StatePort = statePort;
            portName = statePort.Name.ToString();
            
            // Set port color based on type
            var color = direction == Direction.Input ? Color.cyan : Color.magenta;
            portColor = color;
        }
        
        public override bool ContainsPoint(Vector2 localPoint)
        {
            // Expand the clickable area slightly
            var rect = this.rect;
            rect.x -= 5;
            rect.y -= 5;
            rect.width += 10;
            rect.height += 10;
            return rect.Contains(localPoint);
        }
    }
    
    /// <summary>
    /// Popup window for editing node properties
    /// </summary>
    public class NodeEditorPopup : PopupWindowContent
    {
        private StateNode _node;
        private Vector2 _scrollPosition;
        
        public NodeEditorPopup(StateNode node)
        {
            _node = node;
        }
        
        public override Vector2 GetWindowSize()
        {
            return new Vector2(300, 400);
        }
        
        public override void OnGUI(Rect rect)
        {
            GUILayout.Label("Node Properties", EditorStyles.boldLabel);
            
            _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition);
            
            // Basic properties
            EditorGUI.BeginChangeCheck();
            
            var newName = EditorGUILayout.TextField("Name", _node.EditorProperty.Name.ToString());
            if (EditorGUI.EndChangeCheck())
            {
                _node.EditorProperty.Name = new Fixed32String(newName);
            }
            
            EditorGUI.BeginChangeCheck();
            var newDescription = EditorGUILayout.TextField("Description", _node.EditorProperty.Description.ToString());
            if (EditorGUI.EndChangeCheck())
            {
                _node.EditorProperty.Description = new Fixed32String(newDescription);
            }
            
            EditorGUI.BeginChangeCheck();
            var newColor = EditorGUILayout.ColorField("Color", _node.EditorProperty.NodeColor);
            if (EditorGUI.EndChangeCheck())
            {
                _node.EditorProperty.NodeColor = newColor;
            }
            
            EditorGUILayout.Space();
            
            // Node-specific properties
            DrawNodeSpecificProperties();
            
            EditorGUILayout.EndScrollView();
            
            EditorGUILayout.Space();
            
            if (GUILayout.Button("Close"))
            {
                editorWindow.Close();
            }
        }
        
        private void DrawNodeSpecificProperties()
        {
            switch (_node)
            {
                case StateActionNode actionNode:
                    DrawActionNodeProperties(actionNode);
                    break;
                case StateConditionNode conditionNode:
                    DrawConditionNodeProperties(conditionNode);
                    break;
                case StateListenerNode listenerNode:
                    DrawListenerNodeProperties(listenerNode);
                    break;
            }
        }
        
        private void DrawActionNodeProperties(StateActionNode actionNode)
        {
            GUILayout.Label("Actions", EditorStyles.boldLabel);
            
            for (int i = 0; i < actionNode.Actions.Count; i++)
            {
                var action = actionNode.Actions[i];
                EditorGUILayout.LabelField($"Action {i}: {action.GetType().Name}");
                
                EditorGUI.indentLevel++;
                action.Duration = EditorGUILayout.FloatField("Duration", action.Duration);
                action.Delay = EditorGUILayout.FloatField("Delay", action.Delay);
                EditorGUI.indentLevel--;
                
                if (GUILayout.Button("Remove"))
                {
                    actionNode.Actions.RemoveAt(i);
                    break;
                }
                
                EditorGUILayout.Space();
            }
            
            if (GUILayout.Button("Add Wait Action"))
            {
                actionNode.Actions.Add(new WaitAction(1.0f));
            }
            
            if (GUILayout.Button("Add Log Action"))
            {
                actionNode.Actions.Add(new LogAction("Hello World"));
            }
        }
        
        private void DrawConditionNodeProperties(StateConditionNode conditionNode)
        {
            GUILayout.Label("Condition", EditorStyles.boldLabel);
            
            if (conditionNode.Condition != null)
            {
                EditorGUILayout.LabelField($"Type: {conditionNode.Condition.GetType().Name}");
            }
            else
            {
                EditorGUILayout.LabelField("No condition set");
            }
            
            if (GUILayout.Button("Set Always True"))
            {
                conditionNode.SetSimpleCondition(new AlwaysTrueCondition());
            }
            
            if (GUILayout.Button("Set Always False"))
            {
                conditionNode.SetSimpleCondition(new AlwaysFalseCondition());
            }
        }
        
        private void DrawListenerNodeProperties(StateListenerNode listenerNode)
        {
            GUILayout.Label("Event Listener", EditorStyles.boldLabel);
            
            var eventName = EditorGUILayout.TextField("Event Name", listenerNode.EventType.Name.ToString());
            if (eventName != listenerNode.EventType.Name.ToString())
            {
                listenerNode.EventType = new EventType(eventName);
            }
        }
    }
}

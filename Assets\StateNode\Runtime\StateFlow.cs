using System;
using System.Collections.Generic;
using System.Linq;

namespace StateScript
{
    /// <summary>
    /// Main class that manages state flow execution
    /// Contains nodes, connections, variables, and handles execution logic
    /// </summary>
    public class StateFlow : ISerializable
    {
        public Fixed32String Name { get; set; }
        public StateFlowStatus Status { get; set; }
        
        public List<StateNode> Nodes { get; set; } = new List<StateNode>();
        public List<StateConnection> Connections { get; set; } = new List<StateConnection>();
        public List<StateVariable> Variables { get; set; } = new List<StateVariable>();
        
        public NodeId EntryNodeId { get; set; }
        public StateNode CurrentNode { get; private set; }
        
        // Performance caches - built when flow starts
        private Dictionary<NodeId, StateNode> _nodeCache;
        private Dictionary<Fixed32String, StateVariable> _variableCache;
        private Dictionary<EventType, List<StateListenerNode>> _eventListeners;
        private Dictionary<PortId, StatePort> _portCache;
        private Dictionary<PortId, List<PortId>> _connectionCache;
        
        public StateFlow()
        {
            Name = new Fixed32String("New StateFlow");
            Status = StateFlowStatus.None;
        }
        
        public StateFlow(string name)
        {
            Name = new Fixed32String(name);
            Status = StateFlowStatus.None;
        }
        
        #region Variable Access Methods
        
        /// <summary>
        /// Get a strongly typed variable by name
        /// </summary>
        public T GetVariable<T>(string variableName) where T : StateVariable
        {
            var fixedName = new Fixed32String(variableName);
            return _variableCache?.TryGetValue(fixedName, out var variable) == true ? variable as T : null;
        }
        
        /// <summary>
        /// Get integer variable value
        /// </summary>
        public int GetInt(string variableName)
        {
            var variable = GetVariable<StateVariableInt>(variableName);
            return variable?.Value ?? 0;
        }
        
        /// <summary>
        /// Get float variable value
        /// </summary>
        public float GetFloat(string variableName)
        {
            var variable = GetVariable<StateVariableFloat>(variableName);
            return variable?.Value ?? 0f;
        }
        
        /// <summary>
        /// Get boolean variable value
        /// </summary>
        public bool GetBool(string variableName)
        {
            var variable = GetVariable<StateVariableBool>(variableName);
            return variable?.Value ?? false;
        }
        
        /// <summary>
        /// Get string variable value
        /// </summary>
        public string GetString(string variableName)
        {
            var variable = GetVariable<StateVariableString>(variableName);
            return variable?.Value ?? "";
        }
        
        /// <summary>
        /// Set integer variable value
        /// </summary>
        public void SetInt(string variableName, int value)
        {
            var variable = GetVariable<StateVariableInt>(variableName);
            if (variable != null)
                variable.Value = value;
        }
        
        /// <summary>
        /// Set float variable value
        /// </summary>
        public void SetFloat(string variableName, float value)
        {
            var variable = GetVariable<StateVariableFloat>(variableName);
            if (variable != null)
                variable.Value = value;
        }
        
        /// <summary>
        /// Set boolean variable value
        /// </summary>
        public void SetBool(string variableName, bool value)
        {
            var variable = GetVariable<StateVariableBool>(variableName);
            if (variable != null)
                variable.Value = value;
        }
        
        /// <summary>
        /// Set string variable value
        /// </summary>
        public void SetString(string variableName, string value)
        {
            var variable = GetVariable<StateVariableString>(variableName);
            if (variable != null)
                variable.Value = value;
        }
        
        /// <summary>
        /// Add a new variable to the flow
        /// </summary>
        public void AddVariable(StateVariable variable)
        {
            Variables.Add(variable);
            _variableCache?.Add(variable.Name, variable);
        }
        
        /// <summary>
        /// Remove a variable from the flow
        /// </summary>
        public bool RemoveVariable(string variableName)
        {
            var fixedName = new Fixed32String(variableName);
            var variable = Variables.FirstOrDefault(v => v.Name.Equals(fixedName));
            if (variable != null)
            {
                Variables.Remove(variable);
                _variableCache?.Remove(fixedName);
                return true;
            }
            return false;
        }
        
        #endregion
        
        #region Flow Control Methods
        
        /// <summary>
        /// Start the state flow (triggers OnStart event)
        /// </summary>
        public void Start(IStateContext context = null)
        {
            if (Status == StateFlowStatus.Running)
                return;
                
            BuildCaches();
            Status = StateFlowStatus.Running;
            OnEvent(EventType.OnStart, context);
        }
        
        /// <summary>
        /// Trigger an event in the state flow
        /// </summary>
        public void OnEvent(EventType eventType, IStateContext context = null)
        {
            if (Status == StateFlowStatus.None)
                BuildCaches();
            
            // Handle event listeners
            if (_eventListeners.TryGetValue(eventType, out var listeners))
            {
                foreach (var listener in listeners)
                {
                    TransitionToNode(listener.Property.Id, context);
                    break; // Only trigger the first matching listener for now
                }
            }
            
            // Special handling for OnStart with entry node
            if (eventType.Equals(EventType.OnStart) && EntryNodeId.Value != 0)
            {
                TransitionToNode(EntryNodeId, context);
            }
        }
        
        /// <summary>
        /// Update the state flow
        /// </summary>
        public void Update(IStateContext context, float deltaTime)
        {
            if (Status != StateFlowStatus.Running || CurrentNode == null)
                return;
                
            CurrentNode.OnUpdate(this, context, deltaTime);
            
            // Check for node completion and auto-transition
            if (CurrentNode.Status == StateNodeStatus.Completed)
            {
                HandleNodeCompletion(context);
            }
            else if (CurrentNode.Status == StateNodeStatus.Failed)
            {
                Status = StateFlowStatus.Failed;
                OnEvent(EventType.OnFailed, context);
            }
        }
        
        /// <summary>
        /// Pause the state flow
        /// </summary>
        public void Pause()
        {
            if (Status == StateFlowStatus.Running)
            {
                Status = StateFlowStatus.Paused;
                OnEvent(EventType.OnPaused);
            }
        }
        
        /// <summary>
        /// Resume the state flow
        /// </summary>
        public void Resume()
        {
            if (Status == StateFlowStatus.Paused)
            {
                Status = StateFlowStatus.Running;
                OnEvent(EventType.OnResumed);
            }
        }
        
        /// <summary>
        /// Stop the state flow
        /// </summary>
        public void Stop()
        {
            CurrentNode?.OnExit(this, null);
            CurrentNode = null;
            Status = StateFlowStatus.None;
        }
        
        #endregion

        #region Node Management and Navigation

        /// <summary>
        /// Transition to a specific node
        /// </summary>
        public void TransitionToNode(NodeId nodeId, IStateContext context)
        {
            if (_nodeCache.TryGetValue(nodeId, out var newNode))
            {
                CurrentNode?.OnExit(this, context);
                CurrentNode = newNode;
                CurrentNode.OnEnter(this, context);
            }
        }

        /// <summary>
        /// Get nodes connected to the output ports of the specified node
        /// </summary>
        public List<StateNode> GetConnectedNodes(NodeId nodeId)
        {
            var connectedNodes = new List<StateNode>();

            if (!_nodeCache.TryGetValue(nodeId, out var node))
                return connectedNodes;

            var outputPorts = node.Property.GetOutputPorts();
            foreach (var outputPort in outputPorts)
            {
                if (_connectionCache.TryGetValue(outputPort.Id, out var connectedPortIds))
                {
                    foreach (var connectedPortId in connectedPortIds)
                    {
                        if (_portCache.TryGetValue(connectedPortId, out var connectedPort))
                        {
                            if (_nodeCache.TryGetValue(connectedPort.NodeId, out var connectedNode))
                            {
                                connectedNodes.Add(connectedNode);
                            }
                        }
                    }
                }
            }

            return connectedNodes;
        }

        /// <summary>
        /// Get the next node based on current node's output
        /// For condition nodes, this considers the condition result
        /// </summary>
        public StateNode GetNextNode(IStateContext context)
        {
            if (CurrentNode == null)
                return null;

            // Special handling for condition nodes
            if (CurrentNode is StateConditionNode conditionNode)
            {
                var activePort = conditionNode.GetActiveOutputPort(this, context);
                if (activePort != null && _connectionCache.TryGetValue(activePort.Id, out var connectedPortIds))
                {
                    foreach (var connectedPortId in connectedPortIds)
                    {
                        if (_portCache.TryGetValue(connectedPortId, out var connectedPort))
                        {
                            if (_nodeCache.TryGetValue(connectedPort.NodeId, out var nextNode))
                            {
                                return nextNode;
                            }
                        }
                    }
                }
            }
            else
            {
                // For regular nodes, use the first connected output
                var connectedNodes = GetConnectedNodes(CurrentNode.Property.Id);
                return connectedNodes.FirstOrDefault();
            }

            return null;
        }

        /// <summary>
        /// Handle node completion and determine next transition
        /// </summary>
        private void HandleNodeCompletion(IStateContext context)
        {
            var nextNode = GetNextNode(context);
            if (nextNode != null)
            {
                TransitionToNode(nextNode.Property.Id, context);
            }
            else
            {
                // No more nodes to transition to - flow is complete
                Status = StateFlowStatus.Completed;
                OnEvent(EventType.OnComplete, context);
            }
        }

        #endregion

        #region Cache Management and Serialization

        /// <summary>
        /// Build performance caches for fast lookups during execution
        /// </summary>
        private void BuildCaches()
        {
            // Build node cache
            _nodeCache = Nodes.ToDictionary(n => n.Property.Id);

            // Build variable cache
            _variableCache = Variables.ToDictionary(v => v.Name);

            // Build event listener cache
            _eventListeners = Nodes.OfType<StateListenerNode>()
                .GroupBy(n => n.EventType)
                .ToDictionary(g => g.Key, g => g.ToList());

            // Build port cache
            _portCache = new Dictionary<PortId, StatePort>();
            foreach (var node in Nodes)
            {
                foreach (var port in node.Property.Ports)
                {
                    _portCache[port.Id] = port;
                }
            }

            // Build connection cache (output port -> list of input ports)
            _connectionCache = new Dictionary<PortId, List<PortId>>();
            foreach (var connection in Connections)
            {
                if (!_connectionCache.ContainsKey(connection.OutputPortId))
                {
                    _connectionCache[connection.OutputPortId] = new List<PortId>();
                }
                _connectionCache[connection.OutputPortId].Add(connection.InputPortId);
            }
        }

        /// <summary>
        /// Add a node to the flow
        /// </summary>
        public void AddNode(StateNode node)
        {
            Nodes.Add(node);
            _nodeCache?.Add(node.Property.Id, node);

            // Update port cache
            if (_portCache != null)
            {
                foreach (var port in node.Property.Ports)
                {
                    _portCache[port.Id] = port;
                }
            }

            // Update event listener cache if it's a listener node
            if (node is StateListenerNode listenerNode && _eventListeners != null)
            {
                if (!_eventListeners.ContainsKey(listenerNode.EventType))
                {
                    _eventListeners[listenerNode.EventType] = new List<StateListenerNode>();
                }
                _eventListeners[listenerNode.EventType].Add(listenerNode);
            }
        }

        /// <summary>
        /// Remove a node from the flow
        /// </summary>
        public bool RemoveNode(NodeId nodeId)
        {
            var node = Nodes.FirstOrDefault(n => n.Property.Id == nodeId);
            if (node != null)
            {
                // Remove connections involving this node
                var portsToRemove = node.Property.Ports.Select(p => p.Id).ToList();
                Connections.RemoveAll(c => portsToRemove.Contains(c.InputPortId) || portsToRemove.Contains(c.OutputPortId));

                // Remove from collections
                Nodes.Remove(node);
                _nodeCache?.Remove(nodeId);

                // Update caches
                if (_portCache != null)
                {
                    foreach (var portId in portsToRemove)
                    {
                        _portCache.Remove(portId);
                    }
                }

                return true;
            }
            return false;
        }

        /// <summary>
        /// Add a connection between two ports
        /// </summary>
        public void AddConnection(PortId outputPortId, PortId inputPortId)
        {
            var connection = new StateConnection(outputPortId, inputPortId);
            Connections.Add(connection);

            // Update connection cache
            if (_connectionCache != null)
            {
                if (!_connectionCache.ContainsKey(outputPortId))
                {
                    _connectionCache[outputPortId] = new List<PortId>();
                }
                _connectionCache[outputPortId].Add(inputPortId);
            }
        }

        /// <summary>
        /// Remove a connection
        /// </summary>
        public bool RemoveConnection(PortId outputPortId, PortId inputPortId)
        {
            var connection = Connections.FirstOrDefault(c => c.OutputPortId == outputPortId && c.InputPortId == inputPortId);
            if (connection != null)
            {
                Connections.Remove(connection);

                // Update connection cache
                if (_connectionCache != null && _connectionCache.ContainsKey(outputPortId))
                {
                    _connectionCache[outputPortId].Remove(inputPortId);
                    if (_connectionCache[outputPortId].Count == 0)
                    {
                        _connectionCache.Remove(outputPortId);
                    }
                }

                return true;
            }
            return false;
        }

        public void Serialize(ISerializer serializer)
        {
            serializer.WriteString("name", Name.ToString());
            serializer.WriteInt("status", (int)Status);
            serializer.WriteInt("entryNodeId", EntryNodeId.Value);

            // Serialize variables
            serializer.WriteInt("variableCount", Variables.Count);
            for (int i = 0; i < Variables.Count; i++)
            {
                serializer.WriteObject($"variable_{i}", Variables[i]);
            }

            // Serialize nodes
            serializer.WriteInt("nodeCount", Nodes.Count);
            for (int i = 0; i < Nodes.Count; i++)
            {
                serializer.WriteObject($"node_{i}", Nodes[i]);
            }

            // Serialize connections
            serializer.WriteInt("connectionCount", Connections.Count);
            for (int i = 0; i < Connections.Count; i++)
            {
                serializer.WriteObject($"connection_{i}", Connections[i]);
            }
        }

        public void Deserialize(ISerializer serializer)
        {
            Name = new Fixed32String(serializer.ReadString("name"));
            Status = (StateFlowStatus)serializer.ReadInt("status");
            EntryNodeId = serializer.ReadInt("entryNodeId");

            // Deserialize variables
            var variableCount = serializer.ReadInt("variableCount");
            Variables.Clear();
            for (int i = 0; i < variableCount; i++)
            {
                var variableData = serializer.GetSubSerializer($"variable_{i}");
                var variableType = variableData.ReadString("type");
                var variable = StateVariableFactory.CreateVariable(variableType);
                variable.Deserialize(variableData);
                Variables.Add(variable);
            }

            // Deserialize nodes
            var nodeCount = serializer.ReadInt("nodeCount");
            Nodes.Clear();
            for (int i = 0; i < nodeCount; i++)
            {
                var nodeData = serializer.GetSubSerializer($"node_{i}");
                var nodeType = nodeData.ReadString("nodeType");
                var node = StateNodeFactory.CreateNode(nodeType);
                node.Deserialize(nodeData);
                Nodes.Add(node);
            }

            // Deserialize connections
            var connectionCount = serializer.ReadInt("connectionCount");
            Connections.Clear();
            for (int i = 0; i < connectionCount; i++)
            {
                var connection = serializer.ReadObject<StateConnection>($"connection_{i}");
                Connections.Add(connection);
            }

            // Rebuild caches after deserialization
            BuildCaches();
        }

        #endregion

        public override string ToString()
        {
            return $"StateFlow: {Name} ({Status})";
        }
    }

# StateScript Usage Guide

## Overview

StateScript is a node-based state flow system for Unity that allows you to create complex game logic through visual node graphs. It's designed for extensibility and performance, supporting everything from simple state machines to complex skill systems, tutorials, and level flows.

## Quick Start

### 1. Create a StateFlow Asset

1. Right-click in your Project window
2. Create a new C# script that inherits from `StateFlowAsset`
3. Or use the StateFlow Editor to create a new asset

### 2. Open the StateFlow Editor

1. Go to `Window > StateScript > StateFlow Editor`
2. Or select a StateFlowAsset and click "Open StateFlow Editor" in the inspector

### 3. Create Your First Flow

1. Right-click in the graph view to create nodes:
   - **Action Node**: Executes multiple actions simultaneously
   - **Condition Node**: Branches based on conditions
   - **Listener Node**: Entry points triggered by events

2. Connect nodes by dragging from output ports to input ports

3. Add variables in the left panel for data storage

### 4. Use in Your Game

```csharp
using StateScript;
using StateScript.Examples;

public class MyGameObject : MonoBehaviour, IStateContext
{
    [SerializeField] private StateFlowAsset stateFlowAsset;
    private StateFlow runtimeFlow;
    
    void Start()
    {
        // Create runtime instance
        runtimeFlow = stateFlowAsset.CreateRuntimeInstance();
        
        // Start the flow
        runtimeFlow.Start(this);
    }
    
    void Update()
    {
        // Update the flow
        if (runtimeFlow.Status == StateFlowStatus.Running)
        {
            runtimeFlow.Update(this, Time.deltaTime);
        }
    }
    
    // Trigger custom events
    public void OnPlayerDied()
    {
        runtimeFlow.OnEvent(new EventType("PlayerDied"), this);
    }
}
```

## Node Types

### Action Node
Executes multiple actions simultaneously. Each action has its own delay and duration.

**Built-in Actions:**
- `WaitAction`: Waits for a specified duration
- `LogAction`: Logs a message to the console
- `MoveAction`: Moves a transform (example)
- `RotateAction`: Rotates a transform (example)
- `SetVariableAction`: Sets a StateFlow variable (example)

### Condition Node
Evaluates conditions and branches to different outputs.

**Built-in Conditions:**
- `AlwaysTrueCondition`: Always returns true
- `AlwaysFalseCondition`: Always returns false
- `IntCompareCondition`: Compares integer variables

### Listener Node
Entry points that respond to events. Set the event name to listen for specific events.

**Predefined Events:**
- `OnStart`: Default entry point
- `OnComplete`: Flow completed
- `OnFailed`: Flow failed
- `OnPaused`: Flow paused
- `OnResumed`: Flow resumed

## Variables

StateScript supports strongly-typed variables:
- `StateVariableInt`
- `StateVariableFloat`
- `StateVariableBool`
- `StateVariableString`
- `StateVariableVector3`

Access variables through the StateFlow:
```csharp
// Get values
int health = stateFlow.GetInt("PlayerHealth");
float speed = stateFlow.GetFloat("MoveSpeed");
bool isAlive = stateFlow.GetBool("IsAlive");

// Set values
stateFlow.SetInt("PlayerHealth", 100);
stateFlow.SetFloat("MoveSpeed", 5.0f);
stateFlow.SetBool("IsAlive", true);
```

## Creating Custom Actions

```csharp
using StateScript;

public class MyCustomAction : StateAction
{
    public override string TypeName => "MyCustom";
    
    public string Message { get; set; } = "Hello";
    
    public override void OnEnter(StateFlow stateFlow, IStateContext context)
    {
        Status = StateActionStatus.Running;
        Debug.Log($"Custom Action: {Message}");
    }
    
    public override void OnUpdate(StateFlow stateFlow, IStateContext context, float deltaTime)
    {
        // Update logic here
    }
    
    public override void OnExit(StateFlow stateFlow, IStateContext context)
    {
        Status = StateActionStatus.Completed;
    }
    
    public override void Serialize(ISerializer serializer)
    {
        base.Serialize(serializer);
        serializer.WriteString("message", Message);
    }
    
    public override void Deserialize(ISerializer serializer)
    {
        base.Deserialize(serializer);
        Message = serializer.ReadString("message");
    }
}

// Register the action
StateActionFactory.RegisterActionType<MyCustomAction>("MyCustom");
```

## Creating Custom Conditions

```csharp
using StateScript;

public class HealthCondition : StateCondition
{
    public override string TypeName => "Health";
    
    public string VariableName { get; set; } = "PlayerHealth";
    public int Threshold { get; set; } = 50;
    
    public override bool Evaluate(StateFlow stateFlow, IStateContext context)
    {
        var health = stateFlow.GetInt(VariableName);
        return health > Threshold;
    }
    
    // Implement Serialize/Deserialize methods...
}

// Register the condition
StateConditionFactory.RegisterConditionType<HealthCondition>("Health");
```

## Performance Tips

1. **Use Fixed32String**: Variable and event names use stack-allocated strings for performance
2. **Cache References**: StateFlow builds internal caches for fast lookups
3. **Minimize Allocations**: Actions are reused, not recreated during runtime
4. **Batch Updates**: Multiple StateFlows can share the same update loop

## Architecture

- **StateFlow**: Main container for nodes, connections, and variables
- **StateNode**: Base class for all node types
- **StateAction**: Base class for actions that run within ActionNodes
- **StateCondition**: Base class for conditions used in ConditionNodes
- **StateVariable**: Base class for strongly-typed variables
- **IStateContext**: Interface for sharing data between StateFlows

## Event System

StateScript uses a flexible event system:

```csharp
// Trigger events
stateFlow.OnEvent(new EventType("CustomEvent"), context);

// Listen for events with StateListenerNode
// Set the event name in the node properties
```

## Serialization

StateScript uses JSON serialization by default:
- Human-readable format for debugging
- Version control friendly
- Easy to modify externally
- Binary serialization planned for builds

## Editor Features

- **Visual Node Editor**: Drag-and-drop node creation and connection
- **Variable Manager**: Add, edit, and remove variables
- **Node Inspector**: Edit node properties and actions
- **Validation**: Check for common issues like disconnected nodes
- **Real-time Debugging**: See current node and variable values during play

## Best Practices

1. **Use Descriptive Names**: Name your nodes, variables, and events clearly
2. **Keep Flows Simple**: Break complex logic into multiple smaller flows
3. **Test Thoroughly**: Use the validation system and runtime debugging
4. **Document Your Logic**: Use node descriptions to explain complex flows
5. **Version Control**: StateFlow assets are JSON-based and merge-friendly

## Troubleshooting

**Flow doesn't start:**
- Check that you have an entry node or OnStart listener
- Verify the StateFlowAsset is assigned
- Ensure Start() is called on the StateFlow

**Nodes don't execute:**
- Check connections between nodes
- Verify node conditions are met
- Use the debugger to see current node status

**Variables not updating:**
- Ensure variable names match exactly
- Check variable types are correct
- Verify the variable exists in the flow

**Performance issues:**
- Minimize the number of active StateFlows
- Use object pooling for frequently created contexts
- Profile your custom actions and conditions

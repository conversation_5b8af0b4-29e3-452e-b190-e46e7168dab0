using System;
using System.Collections.Generic;

namespace StateScript
{
    /// <summary>
    /// Factory for creating state actions based on type discriminator
    /// </summary>
    public static class StateActionFactory
    {
        private static readonly Dictionary<string, Func<StateAction>> _typeMap = 
            new Dictionary<string, Func<StateAction>>
            {
                { "Wait", () => new WaitAction() },
                { "Log", () => new LogAction() }
            };
        
        /// <summary>
        /// Create a state action instance based on type name
        /// </summary>
        public static StateAction CreateAction(string typeName)
        {
            if (_typeMap.TryGetValue(typeName, out var factory))
            {
                return factory();
            }
            
            throw new ArgumentException($"Unknown action type: {typeName}");
        }
        
        /// <summary>
        /// Register a custom action type
        /// </summary>
        public static void RegisterActionType<T>(string typeName) where T : StateAction, new()
        {
            _typeMap[typeName] = () => new T();
        }
        
        /// <summary>
        /// Get all registered type names
        /// </summary>
        public static IEnumerable<string> GetRegisteredTypes()
        {
            return _typeMap.Keys;
        }
        
        /// <summary>
        /// Check if a type is registered
        /// </summary>
        public static bool IsTypeRegistered(string typeName)
        {
            return _typeMap.ContainsKey(typeName);
        }
    }
}

using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using UnityEngine.UIElements;
using UnityEditor.Experimental.GraphView;
using StateScript;

namespace StateScript.Editor
{
    /// <summary>
    /// GraphView implementation for StateFlow node editing
    /// </summary>
    public class StateFlowGraphView : GraphView
    {
        public event Action<StateNode> OnNodeSelected;
        
        private StateFlowEditorWindow _editorWindow;
        private StateFlow _stateFlow;
        private Dictionary<NodeId, StateNodeView> _nodeViews = new Dictionary<NodeId, StateNodeView>();
        
        public StateFlowGraphView(StateFlowEditorWindow editorWindow)
        {
            _editorWindow = editorWindow;
            
            // Configure GraphView
            SetupZoom(ContentZoomer.DefaultMinScale, ContentZoomer.DefaultMaxScale);
            
            this.AddManipulator(new ContentDragger());
            this.AddManipulator(new SelectionDragger());
            this.AddManipulator(new RectangleSelector());
            
            // Add grid background
            var grid = new GridBackground();
            Insert(0, grid);
            grid.StretchToParentSize();
            
            // Setup context menu
            nodeCreationRequest = OnNodeCreationRequest;
            
            // Handle selection changes
            graphViewChanged += OnGraphViewChanged;
        }
        
        public void LoadStateFlow(StateFlow stateFlow)
        {
            _stateFlow = stateFlow;
            
            // Clear existing content
            ClearGraph();
            
            if (stateFlow == null)
                return;
            
            // Create node views
            foreach (var node in stateFlow.Nodes)
            {
                CreateNodeView(node);
            }
            
            // Create connections
            foreach (var connection in stateFlow.Connections)
            {
                CreateConnectionView(connection);
            }
        }
        
        private void ClearGraph()
        {
            // Remove all nodes and edges
            foreach (var node in nodes.ToList())
            {
                RemoveElement(node);
            }
            
            foreach (var edge in edges.ToList())
            {
                RemoveElement(edge);
            }
            
            _nodeViews.Clear();
        }
        
        private void CreateNodeView(StateNode node)
        {
            var nodeView = new StateNodeView(node);
            nodeView.SetPosition(new Rect(node.EditorProperty.Position, Vector2.zero));
            
            // Handle node selection
            nodeView.OnNodeSelected += (selectedNode) => OnNodeSelected?.Invoke(selectedNode);
            
            AddElement(nodeView);
            _nodeViews[node.Property.Id] = nodeView;
        }
        
        private void CreateConnectionView(StateConnection connection)
        {
            // Find the ports
            StatePort outputPort = null;
            StatePort inputPort = null;
            
            foreach (var nodeView in _nodeViews.Values)
            {
                var foundOutputPort = nodeView.StateNode.Property.Ports.FirstOrDefault(p => p.Id == connection.OutputPortId);
                if (foundOutputPort != null)
                {
                    outputPort = foundOutputPort;
                }
                
                var foundInputPort = nodeView.StateNode.Property.Ports.FirstOrDefault(p => p.Id == connection.InputPortId);
                if (foundInputPort != null)
                {
                    inputPort = foundInputPort;
                }
            }
            
            if (outputPort != null && inputPort != null)
            {
                // Find the corresponding port views
                var outputNodeView = _nodeViews.Values.FirstOrDefault(nv => nv.StateNode.Property.Id == outputPort.NodeId);
                var inputNodeView = _nodeViews.Values.FirstOrDefault(nv => nv.StateNode.Property.Id == inputPort.NodeId);
                
                if (outputNodeView != null && inputNodeView != null)
                {
                    var outputPortView = outputNodeView.GetPortView(outputPort.Id);
                    var inputPortView = inputNodeView.GetPortView(inputPort.Id);
                    
                    if (outputPortView != null && inputPortView != null)
                    {
                        var edge = outputPortView.ConnectTo(inputPortView);
                        AddElement(edge);
                    }
                }
            }
        }
        
        private void OnNodeCreationRequest(NodeCreationContext context)
        {
            // Create context menu for node creation
            var menu = new GenericMenu();
            
            menu.AddItem(new GUIContent("Action Node"), false, () => CreateNode<StateActionNode>(context.screenMousePosition));
            menu.AddItem(new GUIContent("Condition Node"), false, () => CreateNode<StateConditionNode>(context.screenMousePosition));
            menu.AddItem(new GUIContent("Listener Node"), false, () => CreateNode<StateListenerNode>(context.screenMousePosition));
            
            menu.ShowAsContext();
        }
        
        private void CreateNode<T>(Vector2 screenPosition) where T : StateNode, new()
        {
            if (_stateFlow == null)
                return;
            
            var worldPosition = contentViewContainer.WorldToLocal(screenPosition);
            
            var node = new T();
            node.EditorProperty.Position = worldPosition;
            node.EditorProperty.Name = new Fixed32String(typeof(T).Name);
            
            _stateFlow.AddNode(node);
            CreateNodeView(node);
            
            // Mark as dirty
            EditorUtility.SetDirty(_editorWindow);
        }
        
        private GraphViewChange OnGraphViewChanged(GraphViewChange graphViewChange)
        {
            if (_stateFlow == null)
                return graphViewChange;
            
            // Handle node deletions
            if (graphViewChange.elementsToRemove != null)
            {
                foreach (var element in graphViewChange.elementsToRemove)
                {
                    if (element is StateNodeView nodeView)
                    {
                        _stateFlow.RemoveNode(nodeView.StateNode.Property.Id);
                        _nodeViews.Remove(nodeView.StateNode.Property.Id);
                    }
                    else if (element is Edge edge)
                    {
                        // Handle connection removal
                        var outputPort = edge.output as StatePortView;
                        var inputPort = edge.input as StatePortView;
                        
                        if (outputPort != null && inputPort != null)
                        {
                            _stateFlow.RemoveConnection(outputPort.StatePort.Id, inputPort.StatePort.Id);
                        }
                    }
                }
            }
            
            // Handle edge creation
            if (graphViewChange.edgesToCreate != null)
            {
                foreach (var edge in graphViewChange.edgesToCreate)
                {
                    var outputPort = edge.output as StatePortView;
                    var inputPort = edge.input as StatePortView;
                    
                    if (outputPort != null && inputPort != null)
                    {
                        _stateFlow.AddConnection(outputPort.StatePort.Id, inputPort.StatePort.Id);
                    }
                }
            }
            
            // Handle node moves
            if (graphViewChange.movedElements != null)
            {
                foreach (var element in graphViewChange.movedElements)
                {
                    if (element is StateNodeView nodeView)
                    {
                        nodeView.StateNode.EditorProperty.Position = nodeView.GetPosition().position;
                    }
                }
            }
            
            // Mark as dirty for any changes
            EditorUtility.SetDirty(_editorWindow);
            
            return graphViewChange;
        }
        
        public override List<Port> GetCompatiblePorts(Port startPort, NodeAdapter nodeAdapter)
        {
            var compatiblePorts = new List<Port>();
            
            foreach (var port in ports.ToList())
            {
                if (startPort != port && 
                    startPort.node != port.node && 
                    startPort.direction != port.direction)
                {
                    compatiblePorts.Add(port);
                }
            }
            
            return compatiblePorts;
        }
    }
}

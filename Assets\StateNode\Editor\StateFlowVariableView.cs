using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using UnityEngine.UIElements;
using StateScript;

namespace StateScript.Editor
{
    /// <summary>
    /// UI component for managing StateFlow variables
    /// </summary>
    public class StateFlowVariableView : VisualElement
    {
        public event Action OnVariableChanged;
        
        private List<StateVariable> _variables;
        private VisualElement _variableContainer;
        private Button _addButton;
        
        public StateFlowVariableView()
        {
            CreateUI();
        }
        
        private void CreateUI()
        {
            style.backgroundColor = new Color(0.25f, 0.25f, 0.25f, 1f);
            style.paddingTop = 5;
            style.paddingBottom = 5;
            style.paddingLeft = 5;
            style.paddingRight = 5;
            
            // Title
            var title = new Label("Variables");
            title.style.fontSize = 14;
            title.style.unityFontStyleAndWeight = FontStyle.Bold;
            title.style.marginBottom = 5;
            Add(title);
            
            // Variable container
            _variableContainer = new VisualElement();
            Add(_variableContainer);
            
            // Add button
            _addButton = new Button(ShowAddVariableMenu) { text = "Add Variable" };
            _addButton.style.marginTop = 5;
            Add(_addButton);
        }
        
        public void LoadVariables(List<StateVariable> variables)
        {
            _variables = variables;
            RefreshVariableList();
        }
        
        private void RefreshVariableList()
        {
            _variableContainer.Clear();
            
            if (_variables == null)
                return;
            
            for (int i = 0; i < _variables.Count; i++)
            {
                var variable = _variables[i];
                var variableElement = CreateVariableElement(variable, i);
                _variableContainer.Add(variableElement);
            }
        }
        
        private VisualElement CreateVariableElement(StateVariable variable, int index)
        {
            var container = new VisualElement();
            container.style.backgroundColor = new Color(0.3f, 0.3f, 0.3f, 1f);
            container.style.marginBottom = 2;
            container.style.paddingTop = 3;
            container.style.paddingBottom = 3;
            container.style.paddingLeft = 5;
            container.style.paddingRight = 5;
            container.style.borderTopLeftRadius = 3;
            container.style.borderTopRightRadius = 3;
            container.style.borderBottomLeftRadius = 3;
            container.style.borderBottomRightRadius = 3;
            
            // Header with name and type
            var header = new VisualElement();
            header.style.flexDirection = FlexDirection.Row;
            header.style.justifyContent = Justify.SpaceBetween;
            
            var nameLabel = new Label($"{variable.Name} ({variable.TypeName})");
            nameLabel.style.fontSize = 12;
            nameLabel.style.unityFontStyleAndWeight = FontStyle.Bold;
            
            var deleteButton = new Button(() => DeleteVariable(index)) { text = "×" };
            deleteButton.style.width = 20;
            deleteButton.style.height = 20;
            deleteButton.style.fontSize = 12;
            
            header.Add(nameLabel);
            header.Add(deleteButton);
            container.Add(header);
            
            // Value editor
            var valueElement = CreateValueEditor(variable);
            if (valueElement != null)
            {
                container.Add(valueElement);
            }
            
            return container;
        }
        
        private VisualElement CreateValueEditor(StateVariable variable)
        {
            switch (variable)
            {
                case StateVariableInt intVar:
                    return CreateIntEditor(intVar);
                case StateVariableFloat floatVar:
                    return CreateFloatEditor(floatVar);
                case StateVariableBool boolVar:
                    return CreateBoolEditor(boolVar);
                case StateVariableString stringVar:
                    return CreateStringEditor(stringVar);
                case StateVariableVector3 vectorVar:
                    return CreateVector3Editor(vectorVar);
                default:
                    return new Label("Unsupported type");
            }
        }
        
        private VisualElement CreateIntEditor(StateVariableInt intVar)
        {
            var field = new IntegerField();
            field.value = intVar.Value;
            field.RegisterValueChangedCallback(evt =>
            {
                intVar.Value = evt.newValue;
                OnVariableChanged?.Invoke();
            });
            return field;
        }
        
        private VisualElement CreateFloatEditor(StateVariableFloat floatVar)
        {
            var field = new FloatField();
            field.value = floatVar.Value;
            field.RegisterValueChangedCallback(evt =>
            {
                floatVar.Value = evt.newValue;
                OnVariableChanged?.Invoke();
            });
            return field;
        }
        
        private VisualElement CreateBoolEditor(StateVariableBool boolVar)
        {
            var field = new Toggle();
            field.value = boolVar.Value;
            field.RegisterValueChangedCallback(evt =>
            {
                boolVar.Value = evt.newValue;
                OnVariableChanged?.Invoke();
            });
            return field;
        }
        
        private VisualElement CreateStringEditor(StateVariableString stringVar)
        {
            var field = new TextField();
            field.value = stringVar.Value;
            field.RegisterValueChangedCallback(evt =>
            {
                stringVar.Value = evt.newValue;
                OnVariableChanged?.Invoke();
            });
            return field;
        }
        
        private VisualElement CreateVector3Editor(StateVariableVector3 vectorVar)
        {
            var field = new Vector3Field();
            field.value = vectorVar.Value;
            field.RegisterValueChangedCallback(evt =>
            {
                vectorVar.Value = evt.newValue;
                OnVariableChanged?.Invoke();
            });
            return field;
        }
        
        private void ShowAddVariableMenu()
        {
            var menu = new GenericMenu();
            
            menu.AddItem(new GUIContent("Int"), false, () => AddVariable(new StateVariableInt()));
            menu.AddItem(new GUIContent("Float"), false, () => AddVariable(new StateVariableFloat()));
            menu.AddItem(new GUIContent("Bool"), false, () => AddVariable(new StateVariableBool()));
            menu.AddItem(new GUIContent("String"), false, () => AddVariable(new StateVariableString()));
            menu.AddItem(new GUIContent("Vector3"), false, () => AddVariable(new StateVariableVector3()));
            
            menu.ShowAsContext();
        }
        
        private void AddVariable(StateVariable variable)
        {
            if (_variables == null)
                _variables = new List<StateVariable>();
            
            // Generate unique name
            var baseName = $"New{variable.TypeName}";
            var uniqueName = baseName;
            int counter = 1;
            
            while (_variables.Any(v => v.Name.ToString() == uniqueName))
            {
                uniqueName = $"{baseName}{counter}";
                counter++;
            }
            
            variable.Name = new Fixed32String(uniqueName);
            _variables.Add(variable);
            
            RefreshVariableList();
            OnVariableChanged?.Invoke();
        }
        
        private void DeleteVariable(int index)
        {
            if (_variables != null && index >= 0 && index < _variables.Count)
            {
                _variables.RemoveAt(index);
                RefreshVariableList();
                OnVariableChanged?.Invoke();
            }
        }
    }
}
